# app/utils/figma_export.py
# Simple CLI to export assets-only (images and icons) from a Figma file.

import os
import sys
import json
from typing import Any, Dict
from app.utils.figma_utils import get_figma_assets_data

def main() -> None:
    token = os.environ.get("FIGMA_ACCESS_TOKEN")
    link = os.environ.get("FIGMA_FILE_LINK")
    max_icon_size_env = os.environ.get("MAX_ICON_SIZE")
    try:
        max_icon_size = int(max_icon_size_env) if max_icon_size_env else 256
    except ValueError:
        max_icon_size = 256

    if not token or not link:
        print("Set FIGMA_ACCESS_TOKEN and FIGMA_FILE_LINK environment variables to run this script.")
        sys.exit(1)

    try:
        result = get_figma_assets_data(link, token, max_icon_size=max_icon_size)
        with open("assets.json", "w", encoding="utf-8") as f:
            json.dump(result, f, indent=2, ensure_ascii=False)
        total = result.get("counts", {}).get("total", 0)
        print(f"Exported {total} assets to assets.json")
    except Exception as e:
        print(f"Error: {e}")
        sys.exit(2)

if __name__ == "__main__":
    main()