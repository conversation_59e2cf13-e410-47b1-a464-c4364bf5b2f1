# app/utils/figma_utils.py
"""
Figma utilities for extracting assets and processing Figma API data.
"""

import requests
import re
import time
from typing import Optional, List, Dict, Any
from fastapi import HTTPException
from app.connection.tenant_middleware import figma_access_token
import json
import httpx
from app.models.uiux.figma_model import (
    ProcessingStatus,
    FigmaFrameModel,
)
import asyncio
from app.utils.datetime_utils import generate_timestamp


def get_figma_access_token():
    return figma_access_token.get()

def retry_request(func):
    def wrapper(*args, **kwargs):
        max_retries = 5
        retry_delay = 1
        for attempt in range(max_retries):
            try:
                return func(*args, **kwargs)
            except requests.exceptions.RequestException as e:
                if attempt == max_retries - 1:
                    raise HTTPException(status_code=500, detail=f"Failed after {max_retries} attempts: {str(e)}")
                print(f"Request failed, retrying in {retry_delay} seconds...")
                time.sleep(retry_delay)
                retry_delay *= 2  # Exponential backoff
    return wrapper

def extract_file_key(link: str) -> Optional[str]:
    """Extract the Figma file key from a given link."""
    file_match = re.search(r'file/([^/]+)', link)
    if file_match:
        return file_match.group(1)

    design_match = re.search(r'design/([^/]+)', link)
    if design_match:
        return design_match.group(1)

    return None

def extract_frame_data(figma_data: Dict[str, Any]) -> List[Dict[str, Any]]:
    """Extract frame data from the Figma API response recursively."""
    frames = []
    
    def find_frames(node: Dict[str, Any]):
        if node.get("type") == "FRAME":
            frames.append(node)
        
        if "children" in node:
            for child in node["children"]:
                find_frames(child)
    
    if figma_data.get("document"):
        find_frames(figma_data["document"])
        
    return frames
def extract_all_node_data(figma_data: Dict[str, Any]) -> List[Dict[str, Any]]:
    """Extract all node data from the Figma API response recursively."""
    nodes = []
    
    def find_nodes(node: Dict[str, Any]):
        # Add ALL nodes, not just frames
        nodes.append(node)
        
        if "children" in node:
            for child in node["children"]:
                find_nodes(child)
    
    if figma_data.get("document"):
        find_nodes(figma_data["document"])
        
    return nodes






@retry_request
def fetch_frame_images(file_key: str, frame_ids: List[str], limit: int = None) -> Dict[str, str]:
    """
    Fetch image URLs for multiple frames with retry logic and size validation.

    Args:
        file_key: Figma file key
        frame_ids: List of frame IDs to fetch
        limit: Size limit in KB (unused, kept for backward compatibility)
    """
    try:
        url = f"https://api.figma.com/v1/images/{file_key}"
        headers = {"X-Figma-Token": get_figma_access_token()}
        params = {"ids": ",".join(frame_ids)}
        response = requests.get(url, params=params, headers=headers, timeout=300)
        response.raise_for_status()

        images = response.json()['images']

        return images
    except Exception as e:
        raise ValueError(f"Error while fetching images: {str(e)}")

@retry_request
def fetch_frame_image(file_key: str, frame_id: str) -> Optional[str]:
    """Fetch image URL for a single frame with retry logic."""
    url = f"https://api.figma.com/v1/images/{file_key}"
    params = {"ids": frame_id, "scale": 2}
    headers = {"X-Figma-Token": get_figma_access_token()}

    response = requests.get(url, params=params, headers=headers, timeout=300)
    response.raise_for_status()
    return response.json()['images'].get(frame_id)

def get_frame_details(file_key: str, frame_id: str):
    # Fetch frame JSON data
    url = f"https://api.figma.com/v1/files/{file_key}/nodes?ids={frame_id}"
    headers = {"X-Figma-Token": get_figma_access_token()}
    
    response = requests.get(url, headers=headers, timeout=300)
    response.raise_for_status()
    frame_data = response.json()['nodes'][frame_id]['document']

    # Fetch frame image
    image_url = fetch_frame_image(file_key, frame_id)
    
    # Fetch frame thumbnail
    thumbnail_url = f"https://api.figma.com/v1/images/{file_key}?ids={frame_id}&scale=0.5&format=png"
    thumbnail_response = requests.get(thumbnail_url, headers=headers, timeout=300)
    thumbnail_response.raise_for_status()
    thumbnail_url = thumbnail_response.json()['images'].get(frame_id)

    return {
        "frame_id": frame_id,
        "file_key": file_key,
        "json_data": frame_data,
        "imageUrl": image_url,
        "thumbnailUrl": thumbnail_url
    }

def get_figma_file_data_limited(file_link, access_token, byte_limit=None, kb_limit=None, mb_limit=None):
    """Get Figma file data."""
    if not access_token or not access_token.strip():
        raise ValueError("Figma access token is required")

    file_key = extract_file_key(file_link)
    response = requests.get(
        f"https://api.figma.com/v1/files/{file_key}",
        headers={"X-Figma-Token": access_token},
        timeout=300
    )
    response.raise_for_status()
    data = response.json()

    json_str = json.dumps(data)
    size_bytes = len(json_str.encode('utf-8'))
    sizes = {
        "size_bytes": size_bytes,
        "size_kb": size_bytes / 1024,
        "size_mb": size_bytes / (1024 * 1024)
    }

    return data, sizes

def get_figma_file_size(data):
    """Calculate the size of Figma file data."""
    json_str = json.dumps(data)
    size_bytes = len(json_str.encode('utf-8'))
    return {
        "size_bytes": size_bytes,
        "size_kb": size_bytes / 1024,
        "size_mb": size_bytes / (1024 * 1024)
    }

async def get_figma_file_data_limited_async(client: httpx.AsyncClient, figma_link: str, figma_api_key: str) -> tuple:
    """Asynchronously fetch Figma file data."""
    file_key = extract_file_key(figma_link)
    url = f"https://api.figma.com/v1/files/{file_key}"
    headers = {"X-Figma-Token": figma_api_key}

    response = await client.get(url, headers=headers)
    response.raise_for_status()
    data = response.json()

    data_bytes = len(str(data).encode('utf-8'))
    sizes = {
        'size_kb': round(data_bytes / 1024, 2),
        'size_mb': round(data_bytes / (1024 * 1024), 2),
    }

    return data, sizes

async def fetch_frame_images_async(client: httpx.AsyncClient, file_key: str, frame_ids: List[str]) -> Dict[str, str]:
    """
    Asynchronously fetch frame images from Figma API
    """
    url = f"https://api.figma.com/v1/images/{file_key}"
    params = {
        "ids": ",".join(frame_ids),
        "scale": 2,
        "format": "png"
    }
    headers = {"X-Figma-Token": get_figma_access_token()}

    response = await client.get(url, params=params, headers=headers)
    response.raise_for_status()
    return response.json().get('images', {})

async def process_frame(frame: dict, file_key: str, image_urls: Dict[str, str]) -> FigmaFrameModel:
    """Process individual frame and return frame model with status."""
    try:
        frame_model = FigmaFrameModel(
            id=frame["id"],
            name=frame.get("name", "Untitled Frame"),
            status=ProcessingStatus.PROCESSING,
            time_updated=generate_timestamp(),
        )

        if not frame["id"]:
            frame_model.status = ProcessingStatus.FAILED
            frame_model.error_message = "Invalid frame ID"
            return frame_model

        image_url = image_urls.get(frame["id"])
        if not image_url:
            try:
                image_url = fetch_frame_image(file_key, frame["id"])
                await asyncio.sleep(0.1)
            except Exception:
                image_url = None

        if image_url:
            frame_model.imageUrl = image_url
            frame_model.status = ProcessingStatus.COMPLETED
        else:
            frame_model.status = ProcessingStatus.FAILED
            frame_model.error_message = "Failed to fetch frame image"

        if frame.get("absoluteBoundingBox"):
            frame_model.metadata = {
                "width": frame["absoluteBoundingBox"].get("width"),
                "height": frame["absoluteBoundingBox"].get("height"),
            }

        return frame_model

    except Exception as e:
        return FigmaFrameModel(
            id=frame.get("id", "unknown"),
            name=frame.get("name", "Error Frame"),
            status=ProcessingStatus.FAILED,
            error_message=f"Error processing frame: {str(e)}",
            time_updated=generate_timestamp(),
        )


def _analyze_figma_node_patterns(node: Dict[str, Any]) -> Dict[str, Any]:
    """Dynamic asset detection based on Figma API properties."""
    analysis = {
        'is_visual_asset': False,
        'asset_type': None,
        'reasons': []
    }

    # Handle both raw Figma API format and processed format
    node_type = node.get('figma_type', node.get('type', '')).upper()
    name = node.get('name', '').lower()
    fills = node.get('fills', [])
    export_settings = node.get('exportSettings', [])

    if export_settings:
        analysis['is_visual_asset'] = True
        analysis['asset_type'] = 'exportable'
        analysis['reasons'].append('Has exportSettings')
        return analysis

    # Check for processed format images first
    if bool(node.get('imageUrl')):
        analysis['is_visual_asset'] = True
        analysis['asset_type'] = 'image'
        analysis['reasons'].append('Has imageUrl')
        return analysis

    has_image_fill = any(fill.get('type') == 'IMAGE' for fill in fills if isinstance(fill, dict))
    if has_image_fill:
        analysis['is_visual_asset'] = True
        analysis['asset_type'] = 'image'
        analysis['reasons'].append('Has IMAGE fill')
        return analysis

    if node_type == 'IMAGE':
        analysis['is_visual_asset'] = True
        analysis['asset_type'] = 'image'
        analysis['reasons'].append('IMAGE node')
        return analysis

    if node_type in ('COMPONENT', 'INSTANCE', 'COMPONENT_SET'):
        analysis['is_visual_asset'] = True
        analysis['asset_type'] = 'component'
        analysis['reasons'].append(f'{node_type} node')
        return analysis

    if 'image' in name and node_type in ('RECTANGLE', 'FRAME', 'GROUP', 'SECTION'):
        analysis['is_visual_asset'] = True
        analysis['asset_type'] = 'image'
        analysis['reasons'].append('Named as image')
        return analysis

    if node_type in ('VECTOR', 'BOOLEAN_OPERATION', 'STAR', 'POLYGON', 'LINE', 'ELLIPSE', 'REGULAR_POLYGON'):
        has_meaningful_name = name and len(name) > 1 and not name.startswith('vector') and not name.startswith('ellipse') and not name.startswith('rectangle')
        has_visual_styling = fills or node.get('strokes') or node.get('effects')

        if has_meaningful_name or has_visual_styling:
            analysis['is_visual_asset'] = True
            analysis['asset_type'] = 'vector'
            analysis['reasons'].append(f'{node_type} node')
            return analysis

    if node_type == 'TEXT':
        has_visual_styling = fills or node.get('strokes') or node.get('effects')
        has_meaningful_content = node.get('characters', '').strip()

        if has_visual_styling and has_meaningful_content:
            analysis['is_visual_asset'] = True
            analysis['asset_type'] = 'text'
            analysis['reasons'].append('Styled TEXT node')
            return analysis

    if node_type in ('RECTANGLE', 'ELLIPSE') and not 'image' in name:
        has_visual_styling = fills or node.get('strokes') or node.get('effects')
        has_no_children = not node.get('children')

        if has_visual_styling and has_no_children:
            analysis['is_visual_asset'] = True
            analysis['asset_type'] = 'shape'
            analysis['reasons'].append(f'Styled {node_type} node')
            return analysis

    return analysis

def _node_visible(node: Dict[str, Any]) -> bool:
    return node.get("visible", True) is not False

def _has_image_fill(node: Dict[str, Any]) -> bool:
    fills = node.get("fills")
    if not isinstance(fills, list):
        return False
    for fill in fills:
        if isinstance(fill, dict) and fill.get("type") == "IMAGE" and fill.get("visible", True):
            return True
    return False

def _is_icon_like(node: Dict[str, Any]) -> bool:
    """Determine if a Figma node is likely an icon using dynamic analysis."""
    analysis = _analyze_figma_node_patterns(node)
    return analysis['is_visual_asset']

def _create_node_signature(node: Dict[str, Any]) -> str:
    """Dynamic signature based on Figma API properties."""
    node_type = node.get('type', '')
    name = node.get('name', '').strip().lower()
    bbox = node.get('absoluteBoundingBox', {})
    width = round(bbox.get('width', 0))
    height = round(bbox.get('height', 0))

    if node_type in ('COMPONENT', 'INSTANCE'):
        return f"component|{name}"

    if 'image' in name:
        return f"image|{name}"

    return f"{node_type.lower()}|{width}x{height}"


def extract_asset_nodes(figma_data: Dict[str, Any]) -> List[Dict[str, Any]]:
    """Extract assets from Figma file JSON."""
    if not isinstance(figma_data, dict):
        return []

    assets: List[Dict[str, Any]] = []
    seen_ids: set = set()
    seen_signatures: set = set()

    def _should_include_node(node: Dict[str, Any]) -> bool:
        analysis = _analyze_figma_node_patterns(node)
        return analysis['is_visual_asset']

    def _should_deduplicate(node: Dict[str, Any]) -> bool:
        signature = _create_node_signature(node)
        if signature in seen_signatures:
            return True
        seen_signatures.add(signature)
        return False

    def traverse(node: Dict[str, Any]):
        if not isinstance(node, dict) or not _node_visible(node):
            return

        node_id = node.get("id")
        if not node_id:
            for child in node.get("children", []):
                traverse(child)
            return

        ntype = node.get("type")
        if ntype in ("PAGE", "CANVAS"):
            for child in node.get("children", []):
                traverse(child)
            return

        if _should_include_node(node) and node_id not in seen_ids:
            if not _should_deduplicate(node):
                assets.append(node)
                seen_ids.add(node_id)

        for child in node.get("children", []):
            traverse(child)

    # Handle both raw Figma API format and processed format
    document = figma_data.get("document") or figma_data.get("root")
    if document:
        traverse(document)

    return assets

def get_figma_assets_data(figma_link: str, access_token: str, max_icon_size: int = None) -> Dict[str, Any]:
    """Fetch assets and return their rendered image URLs."""

    file_key = extract_file_key(figma_link)
    if not file_key:
        raise ValueError("Invalid Figma link")

    headers = {"X-Figma-Token": access_token}
    file_url = f"https://api.figma.com/v1/files/{file_key}"
    resp = requests.get(file_url, headers=headers, timeout=300)
    resp.raise_for_status()
    data = resp.json()

    asset_nodes = extract_asset_nodes(data, max_icon_size=max_icon_size)
    if not asset_nodes:
        return {
            "fileKey": file_key,
            "counts": {"total": 0, "images": 0, "icons": 0, "components": 0},
            "assets": []
        }

    node_ids = [n["id"] for n in asset_nodes if "id" in n]
    images_url = f"https://api.figma.com/v1/images/{file_key}"
    params = {"ids": ",".join(node_ids), "format": "png", "scale": 2}
    img_resp = requests.get(images_url, headers=headers, params=params, timeout=300)
    img_resp.raise_for_status()
    image_map = img_resp.json().get("images", {})

    assets_out = []
    image_count = icon_count = component_count = 0

    for node in asset_nodes:
        nid = node["id"]
        ntype = node.get("type")
        name = node.get("name") or ""
        has_image_fill = _has_image_fill(node)
        is_icon = _is_icon_like(node, max_icon_size)
        has_export_settings = bool(node.get("exportSettings"))

        if ntype == "IMAGE" or has_image_fill:
            kind = "icon" if is_icon else "image"
            if kind == "icon":
                icon_count += 1
            else:
                image_count += 1
        elif ntype in ("COMPONENT", "INSTANCE"):
            kind = "component"
            component_count += 1
        elif is_icon or has_export_settings:
            kind = "icon"
            icon_count += 1
        else:
            kind = "image"
            image_count += 1

        bbox = node.get("absoluteBoundingBox", {})
        dimensions = None
        if bbox.get("width") and bbox.get("height"):
            dimensions = {"width": bbox["width"], "height": bbox["height"]}

        assets_out.append({
            "id": nid,
            "name": name,
            "type": ntype,
            "kind": kind,
            "imageUrl": image_map.get(nid),
            "dimensions": dimensions,
            "hasExportSettings": has_export_settings,
            "hasImageFill": has_image_fill
        })

    return {
        "fileKey": file_key,
        "counts": {"total": len(assets_out), "images": image_count, "icons": icon_count, "components": component_count},
        "assets": assets_out
    }


