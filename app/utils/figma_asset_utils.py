from typing import List, Dict, Union, <PERSON>ple
import asyncio
import os
import httpx
from app.utils.figma_screen_design_process import get_figma_image_url

__all__ = ["extract_asset_nodes", "process_and_save_images"]

# Complete Figma node type coverage for real-world usage
DIRECT_VISUAL_TYPES = frozenset([
    'IMAGE', 'COMPONENT', 'INSTANCE', 'COMPONENT_SET', 'MEDIA'
])

VECTOR_GRAPHICS_TYPES = frozenset([
    'VECTOR', 'BOOLEAN_OPERATION', 'STAR', 'POLYGON', 'LINE', 'ELLIPSE',
    'REGULAR_POLYGON', 'CONNECTOR', 'STAMP'
])

SHAPE_TYPES = frozenset([
    'RECTANGLE', 'ELLIPSE', 'SHAPE_WITH_TEXT'
])

TEXT_TYPES = frozenset([
    'TEXT', 'TEXT_PATH', 'CODE_BLOCK'
])

CONTAINER_TYPES = frozenset([
    'FRAME', 'GROUP', 'SECTION', 'TABLE', 'TABLE_CELL'
])

INTERACTIVE_TYPES = frozenset([
    'STICKY', 'EMBED', 'LINK_UNFURL', 'WIDGET'
])

UTILITY_TYPES = frozenset([
    'SLICE', 'TRANSFORM_GROUP'
])

# All visual asset types (excluding structural nodes like DOCUMENT, PAGE, CANVAS)
ALL_VISUAL_TYPES = (DIRECT_VISUAL_TYPES | VECTOR_GRAPHICS_TYPES | SHAPE_TYPES |
                   TEXT_TYPES | CONTAINER_TYPES | INTERACTIVE_TYPES | UTILITY_TYPES)

# Priority for selecting primary node (lower number = higher priority)
TYPE_PRIORITY = {
    # Direct visual content (highest priority)
    "IMAGE": 0, "MEDIA": 1, "VECTOR": 2, "COMPONENT": 3, "INSTANCE": 4,

    # Shapes and graphics
    "RECTANGLE": 5, "ELLIPSE": 6, "POLYGON": 7, "STAR": 8, "BOOLEAN_OPERATION": 9,
    "LINE": 10, "CONNECTOR": 11, "STAMP": 12, "SHAPE_WITH_TEXT": 13,

    # Text elements
    "TEXT": 14, "TEXT_PATH": 15, "CODE_BLOCK": 16,

    # Interactive elements
    "STICKY": 17, "EMBED": 18, "LINK_UNFURL": 19, "WIDGET": 20,

    # Containers and structure
    "COMPONENT_SET": 21, "FRAME": 22, "GROUP": 23, "SECTION": 24,
    "TABLE": 25, "TABLE_CELL": 26,

    # Utility types (lowest priority)
    "SLICE": 27, "TRANSFORM_GROUP": 28
}

async def get_figma_image_url_oauth(frame_ids: Union[str, List[str]], access_token: str, file_key: str) -> Dict[str, str]:
    """Fetch Figma image URLs using OAuth token"""
    if isinstance(frame_ids, str):
        frame_ids = [frame_ids]

    try:
        async with httpx.AsyncClient(timeout=30.0) as client:
            response = await client.get(
                f"https://api.figma.com/v1/images/{file_key}",
                params={"ids": ",".join(frame_ids), "scale": 2, "format": "png"},
                headers={"Authorization": f"Bearer {access_token}"}
            )

            if response.status_code == 200:
                images = response.json().get('images', {})
                return {fid: images.get(fid, "") for fid in frame_ids if images.get(fid)}
            return {}
    except Exception:
        return {}


def _is_visual_asset(node: dict) -> tuple[bool, str]:
    """Comprehensive visual asset detection for all Figma node types"""
    node_type = node.get('figma_type', node.get('type', '')).upper()
    node_name = (node.get('name', '') or '').lower()

    # Fast path: nodes with existing image URLs or export settings
    if node.get('imageUrl') or node.get('exportSettings'):
        return True, 'image'

    # Direct visual content types
    if node_type in DIRECT_VISUAL_TYPES:
        if node_type in {'COMPONENT', 'INSTANCE', 'COMPONENT_SET'}:
            return True, 'component'
        elif node_type in {'IMAGE', 'MEDIA'}:
            return True, 'image'
        else:
            return True, 'visual'

    # Vector graphics and illustrations
    if node_type in VECTOR_GRAPHICS_TYPES:
        has_styling = bool(node.get('fills') or node.get('strokes') or node.get('effects'))
        has_meaningful_name = node_name and not node_name.startswith(('vector', 'ellipse', 'rectangle', 'line'))
        if has_styling or has_meaningful_name:
            return True, 'vector'

    # Shape elements with visual styling
    if node_type in SHAPE_TYPES:
        has_styling = bool(node.get('fills') or node.get('strokes') or node.get('effects'))
        if has_styling and not node.get('children'):  # Avoid containers
            return True, 'shape'

    # Text elements (including rich text and code blocks)
    if node_type in TEXT_TYPES:
        has_styling = bool(node.get('fills') or node.get('strokes') or node.get('effects'))
        if has_styling or node_type in {'CODE_BLOCK', 'TEXT_PATH'}:  # Always include special text types
            return True, 'text'

    # Interactive elements (stickies, embeds, widgets)
    if node_type in INTERACTIVE_TYPES:
        return True, 'interactive'

    # Named image containers (common pattern in Figma designs)
    if 'image' in node_name and node_type in CONTAINER_TYPES:
        return True, 'image'

    # Nodes with IMAGE fills (actual image content)
    fills = node.get('fills', [])
    if any(fill.get('type') == 'IMAGE' for fill in fills):
        return True, 'image'

    # Table elements with content
    if node_type in {'TABLE', 'TABLE_CELL'} and (node.get('fills') or node.get('strokes')):
        return True, 'table'

    return False, None

def _create_signature(node: dict) -> str:
    """Create comprehensive deduplication signature for all Figma node types"""
    node_type = node.get('figma_type', node.get('type', '')).lower()
    name = (node.get('name', '') or '').lower().strip()

    # Get dimensions
    bbox = node.get('absoluteBoundingBox', {})
    dimensions = node.get('dimensions', {})
    width = bbox.get('width', 0) or dimensions.get('width', 0)
    height = bbox.get('height', 0) or dimensions.get('height', 0)

    # Named images (highest specificity)
    if 'image' in name:
        return f"image|{name}"

    # Components and instances (by name for reusability)
    if node_type in ('component', 'instance', 'component_set'):
        return f"component|{name}"

    # Text elements (by content and styling)
    if node_type in ('text', 'text_path', 'code_block'):
        text_content = (node.get('characters', '') or '')[:50]  # First 50 chars
        if text_content:
            return f"text|{name}|{hash(text_content) % 10000}"
        return f"text|{name}"

    # Interactive elements (by name and type)
    if node_type in ('sticky', 'embed', 'link_unfurl', 'widget'):
        return f"interactive|{node_type}|{name}"

    # Media elements (by name)
    if node_type in ('media', 'image'):
        return f"media|{name}"

    # Vector graphics (by type and dimensions for similar shapes)
    if node_type in ('vector', 'boolean_operation', 'star', 'polygon', 'line',
                     'ellipse', 'regular_polygon', 'connector', 'stamp'):
        return f"vector|{node_type}|{int(width)}x{int(height)}"

    # Shapes (by type and dimensions)
    if node_type in ('rectangle', 'ellipse', 'shape_with_text'):
        return f"shape|{node_type}|{int(width)}x{int(height)}"

    # Table elements (by position and content)
    if node_type in ('table', 'table_cell'):
        return f"table|{node_type}|{name}|{int(width)}x{int(height)}"

    # Default: type + dimensions
    return f"{node_type}|{int(width)}x{int(height)}"

async def get_all_image_nodes(node: dict) -> List[dict]:
    """Extract visual asset nodes with optimized traversal"""
    image_nodes = []
    added_ids = set()

    def extract_node_data(node: dict) -> dict:
        """Extract essential node data"""
        return {
            "id": node.get("id", ""),
            "name": node.get("name", "Unknown"),
            "type": node.get('figma_type', node.get('type', 'Unknown')),
            "absoluteBoundingBox": node.get('absoluteBoundingBox'),
            "dimensions": node.get('dimensions'),
            "imageUrl": node.get('imageUrl')
        }

    def traverse(current_node: dict):
        """Optimized traversal with early returns"""
        try:
            is_asset, asset_type = _is_visual_asset(current_node)

            if is_asset:
                node_id = current_node.get("id", "")
                if node_id and node_id not in added_ids:
                    extracted_node = extract_node_data(current_node)
                    extracted_node.update({
                        "asset_category": asset_type,
                        "signature": _create_signature(current_node)
                    })
                    image_nodes.append(extracted_node)
                    added_ids.add(node_id)

            # Traverse children
            for child in current_node.get("children", []):
                traverse(child)

        except Exception:
            pass  # Skip problematic nodes

    traverse(node)
    return image_nodes


def _deduplicate_by_signature(nodes: List[dict]) -> List[dict]:
    """Remove duplicates using visual signatures"""
    seen_signatures = set()
    unique_nodes = []

    for node in nodes:
        signature = node.get('signature', '')
        if signature and signature not in seen_signatures:
            seen_signatures.add(signature)
            unique_nodes.append(node)
        elif not signature:
            unique_nodes.append(node)

    return unique_nodes

def extract_asset_nodes(figma_data: dict) -> List[dict]:
    """Extract and deduplicate asset nodes from Figma data"""
    document = figma_data.get("document") or figma_data.get("root")
    if not document:
        return []

    try:
        loop = asyncio.get_event_loop()
    except RuntimeError:
        loop = asyncio.new_event_loop()
        asyncio.set_event_loop(loop)

    extracted_nodes = loop.run_until_complete(get_all_image_nodes(document))
    return _deduplicate_by_signature(extracted_nodes)


async def batch_fetch_images(image_nodes: List[dict], file_key: str, figma_api_key: str = None, access_token: str = None) -> Dict[str, str]:
    """Fetch images in optimized batches"""
    if not image_nodes:
        return {}

    # Dynamic batch sizing
    total_nodes = len(image_nodes)
    batch_size = min(50, max(10, total_nodes // 4)) if total_nodes > 20 else 10

    # Override from environment
    try:
        env_batch_size = int(os.getenv("FIGMA_BATCH_SIZE", "0"))
        if env_batch_size > 0:
            batch_size = env_batch_size
    except ValueError:
        pass

    all_image_urls = {}
    semaphore = asyncio.Semaphore(int(os.getenv("FIGMA_MAX_CONCURRENT_BATCHES", "3")))

    async def process_batch(batch_nodes: List[dict]) -> Dict[str, str]:
        async with semaphore:
            batch_ids = [node['id'] for node in batch_nodes]

            try:
                if access_token:
                    return await get_figma_image_url_oauth(batch_ids, access_token, file_key)
                else:
                    return await get_figma_image_url(batch_ids, figma_api_key, file_key)
            except Exception:
                return {}

    # Create and execute batch tasks
    batches = [image_nodes[i:i + batch_size] for i in range(0, len(image_nodes), batch_size)]
    tasks = [process_batch(batch) for batch in batches]
    results = await asyncio.gather(*tasks, return_exceptions=True)

    # Combine results
    for result in results:
        if isinstance(result, dict):
            all_image_urls.update(result)

    return all_image_urls


def _pick_primary_node(nodes: List[dict]) -> dict:
    """Select primary node using type priority"""
    def get_rank(node: dict) -> tuple:
        node_type = node.get("type", "")
        priority = TYPE_PRIORITY.get(node_type, 999)
        name_len = len(node.get("name", ""))
        node_id = node.get("id", "")
        return (priority, name_len, node_id)

    return min(nodes, key=get_rank)

def _get_file_extension(content_type: str) -> str:
    """Get file extension from content type"""
    content_type = (content_type or "").lower()
    if "svg" in content_type:
        return ".svg"
    elif "jpeg" in content_type or "jpg" in content_type:
        return ".jpg"
    elif "webp" in content_type:
        return ".webp"
    return ".png"

def _safe_filename(node_id: str) -> str:
    """Create safe filename from node ID"""
    if not node_id:
        return "node"
    safe_id = node_id.replace(":", "_").replace(";", "_")
    return safe_id[1:] if safe_id.startswith("I") else safe_id

async def process_and_save_images(data: dict, file_key: str, output_directory: str,
                                figma_api_key: str = None, access_token: str = None) -> Tuple[List[dict], Dict[str, str], Dict[str, str]]:
    """Process and save images with optimized deduplication"""
    try:
        import hashlib
        os.makedirs(output_directory, exist_ok=True)

        # Extract and deduplicate nodes
        image_nodes = await get_all_image_nodes(data.get('document', {}))
        filtered_nodes = _deduplicate_by_signature(image_nodes)

        # Fetch image URLs
        image_urls = await batch_fetch_images(filtered_nodes, file_key, figma_api_key, access_token)

        # Group nodes by URL
        node_by_id = {n["id"]: n for n in filtered_nodes if n.get("id")}
        url_groups = {}
        for node_id, url in image_urls.items():
            if url and node_id in node_by_id:
                url_groups.setdefault(url, []).append(node_by_id[node_id])

        # Process downloads
        saved_images = []
        url_to_path_mapping = {}
        md5_to_path = {}

        semaphore = asyncio.Semaphore(int(os.getenv("FIGMA_MAX_CONCURRENT_DOWNLOADS", "8")))

        async def download_and_save(url: str, nodes: List[dict]):
            async with semaphore:
                try:
                    primary = _pick_primary_node(nodes)

                    async with httpx.AsyncClient(timeout=30.0) as client:
                        response = await client.get(url)
                        if response.status_code != 200 or not response.content:
                            return

                        content = response.content
                        md5_hash = hashlib.md5(content).hexdigest()

                        # Check if already saved by MD5
                        if md5_hash in md5_to_path:
                            final_path = md5_to_path[md5_hash]
                        else:
                            # Create new file
                            ext = _get_file_extension(response.headers.get("content-type"))
                            safe_id = _safe_filename(primary.get("id", ""))
                            filename = f"figma_image_{safe_id}{ext}"
                            final_path = os.path.join(output_directory, filename)

                            # Handle filename conflicts
                            if os.path.exists(final_path):
                                filename = f"figma_image_{safe_id}_{md5_hash[:8]}{ext}"
                                final_path = os.path.join(output_directory, filename)

                            # Save file
                            with open(final_path, "wb") as f:
                                f.write(content)

                            md5_to_path[md5_hash] = final_path

                        # Update mappings
                        relative_path = f"/assets/figmaimages/{os.path.basename(final_path)}"
                        url_to_path_mapping[url] = relative_path

                        saved_images.append({
                            "id": primary.get("id", ""),
                            "name": primary.get("name", "Unknown"),
                            "type": primary.get("type", "UNKNOWN"),
                            "url": url,
                            "local_path": final_path,
                            "relative_path": relative_path,
                            "is_duplicate": False
                        })

                except Exception as e:
                    print(f"Error downloading {url}: {e}")

        # Execute downloads
        tasks = [download_and_save(url, nodes) for url, nodes in url_groups.items()]
        await asyncio.gather(*tasks, return_exceptions=True)

        return saved_images, url_to_path_mapping, image_urls

    except Exception as e:
        print(f"Error processing images: {e}")
        return [], {}, {}